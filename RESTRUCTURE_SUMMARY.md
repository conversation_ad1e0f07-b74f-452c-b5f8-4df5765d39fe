# MS365 Identity and Access Resource Restructuring

## Summary

This restructuring addresses GitHub issue #5728 by moving the MS365 identity and access policy resources to a more logical hierarchical structure under Privileged Identity Management (PIM).

## Changes Made

### Before (Old Structure)
```
microsoft.identityAndAccess
├── []microsoft.identityAndAccess.policy
├── filter: string
└── roleEligibilityScheduleInstances()
```

### After (New Structure)
```
microsoft.identityAndAccess
├── privilegedIdentityManagement()
│   └── policies()
│       ├── []microsoft.identityAndAccess.privilegedIdentityManagement.policy
│       └── filter: string
└── roleEligibilityScheduleInstances()
```

## Resource Mapping

| Old Resource | New Resource |
|--------------|--------------|
| `microsoft.identityAndAccess.policy` | `microsoft.identityAndAccess.privilegedIdentityManagement.policy` |
| `microsoft.identityAndAccess.policy.rule` | `microsoft.identityAndAccess.privilegedIdentityManagement.policy.rule` |
| `microsoft.identityAndAccess.policy.rule.target` | `microsoft.identityAndAccess.privilegedIdentityManagement.policy.rule.target` |

## New Resources Added

1. **`microsoft.identityAndAccess.privilegedIdentityManagement`** - Container resource for PIM-related functionality
2. **`microsoft.identityAndAccess.privilegedIdentityManagement.policies`** - Collection resource that returns the same data as the old `microsoft.identityAndAccess` list

## Usage Examples

### Old Usage (No Longer Available)
```mql
# This will no longer work
microsoft.identityAndAccess.list
microsoft.identityAndAccess(filter: "scopeId eq '/' and scopeType eq 'DirectoryRole'").list
```

### New Usage
```mql
# Access PIM policies
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list

# Filter PIM policies
microsoft.identityAndAccess.privilegedIdentityManagement.policies(filter: "scopeId eq '/' and scopeType eq 'DirectoryRole'").list

# Access individual policy fields
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list {
  id
  displayName
  description
  isOrganizationDefault
  scopeId
  scopeType
  lastModifiedDateTime
  lastModifiedBy
}

# Access policy rules
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list {
  rules {
    id
    target {
      caller
      level
      operations
    }
  }
}
```

## Implementation Details

1. **Resource Definitions**: Updated `ms365.lr` to define the new resource hierarchy
2. **Go Implementation**: Modified `identity_and_access.go` to implement the new structure
3. **Generated Code**: Regenerated `.lr.go` files using the `lr go` tool
4. **Manifest**: Updated manifest files to reflect the new resource structure
5. **Backward Compatibility**: Old resources were completely removed (no deprecation)

## Testing

The restructuring preserves all existing functionality while providing a cleaner, more logical resource hierarchy. The same data and filtering capabilities are available through the new structure.
