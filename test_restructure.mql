# Test the new MS365 identity and access resource structure
# This file tests that the restructured resources work correctly

# Test that the new privilegedIdentityManagement resource exists
microsoft.identityAndAccess.privilegedIdentityManagement

# Test that the new policies collection resource exists
microsoft.identityAndAccess.privilegedIdentityManagement.policies

# Test that we can access the policies list (this should return the same data as before)
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list

# Test that we can filter policies (same functionality as before)
microsoft.identityAndAccess.privilegedIdentityManagement.policies(filter: "scopeId eq '/' and scopeType eq 'DirectoryRole'").list

# Test that individual policy resources have the expected fields
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list {
  id
  displayName
  description
  isOrganizationDefault
  scopeId
  scopeType
  lastModifiedDateTime
  lastModifiedBy
}

# Test that policy rules still work
microsoft.identityAndAccess.privilegedIdentityManagement.policies.list {
  rules {
    id
    target {
      caller
      level
      operations
    }
  }
}
